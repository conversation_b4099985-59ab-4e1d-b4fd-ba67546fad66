#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 VISUALISEUR ACTIVITÉ JARVIS - JEAN-LUC PASSAVE
Interface pour voir tout ce que JARVIS a fait quand vous n'étiez pas là
"""

import json
import os
import sqlite3
from datetime import datetime, timedelta
import gradio as gr

class VisualiseurActiviteJarvis:
    """VISUALISEUR ACTIVITÉ JARVIS - JEAN-LUC PASSAVE"""
    
    def __init__(self):
        self.db_file = "jarvis_activite_surveillance.db"
        self.log_file = "jarvis_activite_log.json"
    
    def obtenir_resume_activite(self, periode_heures=24):
        """OBTENIR RÉSUMÉ ACTIVITÉ SUR UNE PÉRIODE"""
        try:
            if not os.path.exists(self.db_file):
                return "❌ Aucune base de données de surveillance trouvée. Lancez d'abord surveillance_jarvis_activite.py"
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Calculer la période
            maintenant = datetime.now()
            debut_periode = maintenant - timedelta(hours=periode_heures)
            
            # Compter les activités par type
            cursor.execute('''
                SELECT type_activite, COUNT(*) as count
                FROM activites 
                WHERE timestamp >= ?
                GROUP BY type_activite
                ORDER BY count DESC
            ''', (debut_periode.isoformat(),))
            
            activites_par_type = cursor.fetchall()
            
            # Compter les pensées autonomes
            cursor.execute('''
                SELECT COUNT(*) FROM pensees_autonomes 
                WHERE timestamp >= ?
            ''', (debut_periode.isoformat(),))
            
            pensees_count = cursor.fetchone()[0]
            
            # Obtenir les dernières activités
            cursor.execute('''
                SELECT timestamp, type_activite, description
                FROM activites 
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
                LIMIT 20
            ''', (debut_periode.isoformat(),))
            
            dernieres_activites = cursor.fetchall()
            
            # Obtenir les sessions
            cursor.execute('''
                SELECT debut_session, fin_session, duree_minutes, activites_pendant_session
                FROM sessions_utilisateur 
                WHERE debut_session >= ?
                ORDER BY debut_session DESC
            ''', (debut_periode.isoformat(),))
            
            sessions = cursor.fetchall()
            
            conn.close()
            
            # Construire le rapport
            rapport = f"""
# 📊 RAPPORT D'ACTIVITÉ JARVIS - JEAN-LUC PASSAVE
## Période: Dernières {periode_heures} heures

### 📈 RÉSUMÉ GÉNÉRAL:
- **🧠 Pensées autonomes**: {pensees_count}
- **📊 Total activités**: {sum(count for _, count in activites_par_type)}
- **🕐 Sessions**: {len(sessions)}

### 🎯 ACTIVITÉS PAR TYPE:
"""
            
            for type_activite, count in activites_par_type:
                rapport += f"- **{type_activite}**: {count} fois\n"
            
            rapport += "\n### 🕐 SESSIONS UTILISATEUR:\n"
            for session in sessions:
                debut, fin, duree, activites = session
                debut_dt = datetime.fromisoformat(debut)
                fin_str = f" → {datetime.fromisoformat(fin).strftime('%H:%M:%S')}" if fin else " (en cours)"
                rapport += f"- **{debut_dt.strftime('%Y-%m-%d %H:%M:%S')}{fin_str}** - {duree:.1f}min - {activites} activités\n"
            
            rapport += "\n### 📋 DERNIÈRES ACTIVITÉS:\n"
            for timestamp, type_act, description in dernieres_activites:
                dt = datetime.fromisoformat(timestamp)
                rapport += f"- **{dt.strftime('%H:%M:%S')}** [{type_act}] {description[:100]}...\n"
            
            return rapport
            
        except Exception as e:
            return f"❌ Erreur lecture activité: {e}"
    
    def obtenir_pensees_autonomes(self, limite=50):
        """OBTENIR LES PENSÉES AUTONOMES RÉCENTES"""
        try:
            if not os.path.exists(self.db_file):
                return "❌ Aucune base de données de surveillance trouvée."
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT timestamp, sujet, contenu, complexite
                FROM pensees_autonomes 
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limite,))
            
            pensees = cursor.fetchall()
            conn.close()
            
            if not pensees:
                return "🧠 Aucune pensée autonome enregistrée récemment."
            
            rapport = f"# 🧠 PENSÉES AUTONOMES DE JARVIS\n## {len(pensees)} pensées récentes:\n\n"
            
            for timestamp, sujet, contenu, complexite in pensees:
                dt = datetime.fromisoformat(timestamp)
                rapport += f"""
### 🕐 {dt.strftime('%Y-%m-%d %H:%M:%S')} - Complexité: {complexite}
**Sujet**: {sujet}
**Contenu**: {contenu}
---
"""
            
            return rapport
            
        except Exception as e:
            return f"❌ Erreur lecture pensées: {e}"
    
    def obtenir_activite_detaillee(self, date_specifique=None):
        """OBTENIR ACTIVITÉ DÉTAILLÉE POUR UNE DATE"""
        try:
            if not os.path.exists(self.db_file):
                return "❌ Aucune base de données de surveillance trouvée."
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            if date_specifique:
                # Analyser la date spécifique
                try:
                    date_obj = datetime.strptime(date_specifique, '%Y-%m-%d')
                    debut_jour = date_obj.strftime('%Y-%m-%d 00:00:00')
                    fin_jour = date_obj.strftime('%Y-%m-%d 23:59:59')
                except:
                    return "❌ Format de date invalide. Utilisez YYYY-MM-DD"
            else:
                # Aujourd'hui par défaut
                aujourd_hui = datetime.now()
                debut_jour = aujourd_hui.strftime('%Y-%m-%d 00:00:00')
                fin_jour = aujourd_hui.strftime('%Y-%m-%d 23:59:59')
            
            # Obtenir toutes les activités du jour
            cursor.execute('''
                SELECT timestamp, type_activite, description, details, cpu_percent, memory_percent
                FROM activites 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
            ''', (debut_jour, fin_jour))
            
            activites = cursor.fetchall()
            
            # Obtenir les pensées du jour
            cursor.execute('''
                SELECT timestamp, sujet, contenu
                FROM pensees_autonomes 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
            ''', (debut_jour, fin_jour))
            
            pensees = cursor.fetchall()
            
            conn.close()
            
            date_affichage = debut_jour.split()[0]
            rapport = f"""
# 📅 ACTIVITÉ DÉTAILLÉE JARVIS - {date_affichage}
## Jean-Luc Passave - Surveillance Complète

### 📊 RÉSUMÉ DU JOUR:
- **📋 Total activités**: {len(activites)}
- **🧠 Pensées autonomes**: {len(pensees)}

### 🕐 CHRONOLOGIE COMPLÈTE:
"""
            
            # Fusionner et trier activités et pensées par timestamp
            tous_evenements = []
            
            for timestamp, type_act, desc, details, cpu, mem in activites:
                tous_evenements.append({
                    'timestamp': timestamp,
                    'type': 'activite',
                    'type_activite': type_act,
                    'description': desc,
                    'cpu': cpu,
                    'memory': mem
                })
            
            for timestamp, sujet, contenu in pensees:
                tous_evenements.append({
                    'timestamp': timestamp,
                    'type': 'pensee',
                    'sujet': sujet,
                    'contenu': contenu
                })
            
            # Trier par timestamp
            tous_evenements.sort(key=lambda x: x['timestamp'])
            
            for event in tous_evenements:
                dt = datetime.fromisoformat(event['timestamp'])
                heure = dt.strftime('%H:%M:%S')
                
                if event['type'] == 'activite':
                    cpu_info = f" (CPU: {event['cpu']:.1f}%, RAM: {event['memory']:.1f}%)" if event['cpu'] else ""
                    rapport += f"**{heure}** 📊 [{event['type_activite']}] {event['description']}{cpu_info}\n\n"
                else:
                    rapport += f"**{heure}** 🧠 **PENSÉE**: {event['sujet']}\n   ↳ {event['contenu'][:200]}...\n\n"
            
            return rapport
            
        except Exception as e:
            return f"❌ Erreur lecture activité détaillée: {e}"
    
    def obtenir_statistiques_globales(self):
        """OBTENIR STATISTIQUES GLOBALES"""
        try:
            if not os.path.exists(self.db_file):
                return "❌ Aucune base de données de surveillance trouvée."
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Statistiques générales
            cursor.execute('SELECT COUNT(*) FROM activites')
            total_activites = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM pensees_autonomes')
            total_pensees = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM sessions_utilisateur')
            total_sessions = cursor.fetchone()[0]
            
            # Première et dernière activité
            cursor.execute('SELECT MIN(timestamp), MAX(timestamp) FROM activites')
            premiere, derniere = cursor.fetchone()
            
            # Moyenne CPU/RAM
            cursor.execute('SELECT AVG(cpu_percent), AVG(memory_percent) FROM activites WHERE cpu_percent IS NOT NULL')
            result = cursor.fetchone()
            avg_cpu, avg_ram = result if result else (None, None)

            # Activités par jour
            cursor.execute('''
                SELECT DATE(timestamp) as jour, COUNT(*) as count
                FROM activites
                GROUP BY DATE(timestamp)
                ORDER BY jour DESC
                LIMIT 7
            ''')

            activites_par_jour = cursor.fetchall()

            conn.close()

            # Formatage sécurisé des moyennes
            cpu_str = f"{avg_cpu:.1f}%" if avg_cpu is not None else "N/A"
            ram_str = f"{avg_ram:.1f}%" if avg_ram is not None else "N/A"
            premiere_str = premiere if premiere else "N/A"
            derniere_str = derniere if derniere else "N/A"

            rapport = f"""
# 📈 STATISTIQUES GLOBALES JARVIS
## Jean-Luc Passave - Surveillance Complète

### 🎯 TOTAUX:
- **📊 Total activités**: {total_activites:,}
- **🧠 Total pensées autonomes**: {total_pensees:,}
- **🕐 Total sessions**: {total_sessions:,}

### 📅 PÉRIODE DE SURVEILLANCE:
- **🚀 Première activité**: {premiere_str}
- **⏰ Dernière activité**: {derniere_str}

### 💻 PERFORMANCE MOYENNE:
- **🖥️ CPU moyen**: {cpu_str}
- **💾 RAM moyenne**: {ram_str}

### 📊 ACTIVITÉS PAR JOUR (7 derniers jours):
"""

            for jour, count in activites_par_jour:
                rapport += f"- **{jour}**: {count:,} activités\n"
            
            return rapport
            
        except Exception as e:
            return f"❌ Erreur statistiques: {e}"

def creer_interface():
    """CRÉER INTERFACE GRADIO"""
    visualiseur = VisualiseurActiviteJarvis()
    
    with gr.Blocks(title="🕐 Surveillance Activité JARVIS - Jean-Luc Passave") as interface:
        gr.Markdown("# 🕐 SURVEILLANCE ACTIVITÉ JARVIS\n## Jean-Luc Passave - Voir tout ce que JARVIS fait quand vous n'êtes pas là")
        
        with gr.Tabs():
            with gr.Tab("📊 Résumé Activité"):
                periode_input = gr.Slider(1, 168, value=24, label="Période (heures)")
                resume_output = gr.Markdown()
                btn_resume = gr.Button("🔄 Actualiser Résumé")
                btn_resume.click(
                    visualiseur.obtenir_resume_activite,
                    inputs=[periode_input],
                    outputs=[resume_output]
                )
            
            with gr.Tab("🧠 Pensées Autonomes"):
                limite_pensees = gr.Slider(10, 200, value=50, label="Nombre de pensées")
                pensees_output = gr.Markdown()
                btn_pensees = gr.Button("🧠 Voir Pensées")
                btn_pensees.click(
                    visualiseur.obtenir_pensees_autonomes,
                    inputs=[limite_pensees],
                    outputs=[pensees_output]
                )
            
            with gr.Tab("📅 Activité Détaillée"):
                date_input = gr.Textbox(
                    placeholder="YYYY-MM-DD (laissez vide pour aujourd'hui)",
                    label="Date spécifique"
                )
                detail_output = gr.Markdown()
                btn_detail = gr.Button("📅 Voir Détails")
                btn_detail.click(
                    visualiseur.obtenir_activite_detaillee,
                    inputs=[date_input],
                    outputs=[detail_output]
                )
            
            with gr.Tab("📈 Statistiques Globales"):
                stats_output = gr.Markdown()
                btn_stats = gr.Button("📈 Voir Statistiques")
                btn_stats.click(
                    visualiseur.obtenir_statistiques_globales,
                    outputs=[stats_output]
                )
        
        # Charger résumé initial
        interface.load(
            visualiseur.obtenir_resume_activite,
            inputs=[gr.Number(value=24, visible=False)],
            outputs=[resume_output]
        )
    
    return interface

if __name__ == "__main__":
    print("🕐 LANCEMENT VISUALISEUR ACTIVITÉ JARVIS - JEAN-LUC PASSAVE")
    interface = creer_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=8250,
        share=False,
        show_error=True
    )
    print("🌐 Interface disponible sur: http://localhost:8250")
