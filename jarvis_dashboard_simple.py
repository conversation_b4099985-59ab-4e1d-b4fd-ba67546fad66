#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏠 JARVIS DASHBOARD PRINCIPAL - VERSION SIMPLE
Interface principale validée - Jean-Luc Passave
Port 8101 - Dashboard avec navigation vers surveillance
"""

import gradio as gr
import webbrowser
import subprocess
import os
import time

# ============================================================================
# CONFIGURATION
# ============================================================================

DASHBOARD_PORT = 8101
SURVEILLANCE_PORT = 8260

# ============================================================================
# FONCTIONS DASHBOARD
# ============================================================================

def ouvrir_surveillance():
    """Ouvre l'interface de surveillance"""
    try:
        # Vérifier si l'interface de surveillance est déjà lancée
        import requests
        try:
            response = requests.get(f"http://localhost:{SURVEILLANCE_PORT}", timeout=2)
            if response.status_code == 200:
                webbrowser.open(f"http://localhost:{SURVEILLANCE_PORT}")
                return "✅ Interface de surveillance ouverte"
        except:
            pass
        
        # Lancer l'interface de surveillance
        subprocess.Popen([
            "python3", "jarvis_surveillance_simple.py"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # Attendre un peu puis ouvrir
        time.sleep(3)
        webbrowser.open(f"http://localhost:{SURVEILLANCE_PORT}")
        return "🚀 Interface de surveillance lancée et ouverte"
        
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

def creer_dashboard_principal():
    """Crée le dashboard principal avec navigation"""
    
    with gr.Blocks(
        title="🏠 JARVIS Dashboard Principal - Jean-Luc Passave",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .gr-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-weight: bold;
            border-radius: 10px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .gr-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .gr-button-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        """
    ) as interface:
        
        # En-tête
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px; margin: 10px 0;">
            <h1 style="color: white; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); font-size: 2.5em;">
                🏠 JARVIS Dashboard
            </h1>
            <p style="color: rgba(255,255,255,0.9); margin: 10px 0; font-size: 1.2em;">
                Interface Multi-Fenêtres Professionnelle
            </p>
            <p style="color: rgba(255,255,255,0.8); margin: 5px 0;">
                Jean-Luc Passave - Version Validée
            </p>
            <div style="display: flex; justify-content: center; gap: 20px; margin: 15px 0;">
                <span style="background: rgba(76, 175, 80, 0.8); padding: 8px 15px; border-radius: 20px; color: white; font-weight: bold;">
                    🧠 QI: 648
                </span>
                <span style="background: rgba(33, 150, 243, 0.8); padding: 8px 15px; border-radius: 20px; color: white; font-weight: bold;">
                    🔥 100,000,000 neurones
                </span>
                <span style="background: rgba(255, 152, 0, 0.8); padding: 8px 15px; border-radius: 20px; color: white; font-weight: bold;">
                    ⚡ 15 accélérateurs
                </span>
            </div>
        </div>
        """)
        
        # Fonctionnalités principales
        gr.HTML("""
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin: 20px 0;">
            <h2 style="color: white; margin: 0 0 20px 0; text-align: center;">🎯 Fonctionnalités Principales</h2>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                <div style="background: rgba(76, 175, 80, 0.2); padding: 15px; border-radius: 10px; text-align: center; border: 1px solid rgba(76, 175, 80, 0.3);">
                    <h3 style="margin: 0; color: #4CAF50;">🧠 Intelligence</h3>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">DeepSeek R1 8B</p>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">Mémoire Thermique</p>
                </div>
                <div style="background: rgba(33, 150, 243, 0.2); padding: 15px; border-radius: 10px; text-align: center; border: 1px solid rgba(33, 150, 243, 0.3);">
                    <h3 style="margin: 0; color: #2196F3;">⚡ Performance</h3>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">Multi-Fenêtres</p>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">Optimisé M4</p>
                </div>
                <div style="background: rgba(156, 39, 176, 0.2); padding: 15px; border-radius: 10px; text-align: center; border: 1px solid rgba(156, 39, 176, 0.3);">
                    <h3 style="margin: 0; color: #9C27B0;">🔒 Sécurité</h3>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">100% Local</p>
                    <p style="margin: 5px 0; color: rgba(255,255,255,0.8); font-size: 12px;">Contrôle Total</p>
                </div>
            </div>
        </div>
        """)
        
        # Bouton surveillance - PRINCIPAL
        with gr.Row():
            with gr.Column():
                gr.HTML("""
                <div style="text-align: center; margin: 30px 0; padding: 20px; background: rgba(255,193,7,0.2); border-radius: 15px; border: 2px solid rgba(255,193,7,0.5);">
                    <h2 style="color: #FFC107; margin: 10px 0;">🎯 SURVEILLANCE ACTIVITÉ JARVIS</h2>
                    <p style="color: white; margin: 10px 0; font-size: 1.1em;">
                        Voir tout ce que JARVIS fait quand vous n'êtes pas là
                    </p>
                    <p style="color: rgba(255,255,255,0.8); margin: 5px 0;">
                        Rapports détaillés, historique des actions, monitoring en temps réel
                    </p>
                </div>
                """)
                surveillance_btn = gr.Button("🎯 OUVRIR SURVEILLANCE ACTIVITÉ", variant="primary", size="lg")
        
        # Messages de statut
        status_msg = gr.HTML()
        
        # Connexions
        surveillance_btn.click(
            fn=ouvrir_surveillance,
            outputs=[status_msg]
        )
    
    return interface

# ============================================================================
# LANCEMENT
# ============================================================================

if __name__ == "__main__":
    print("🏠 ================================")
    print("🏠 JARVIS DASHBOARD PRINCIPAL")
    print("🏠 ================================")
    print(f"🌐 Interface: http://localhost:{DASHBOARD_PORT}")
    print("👤 Utilisateur: Jean-Luc Passave")
    print("🎯 Interface Multi-Fenêtres Professionnelle")
    print("✅ Version Validée")
    print("=" * 50)
    
    # Créer et lancer l'interface
    interface = creer_dashboard_principal()
    interface.launch(
        server_name="0.0.0.0",
        server_port=DASHBOARD_PORT,
        share=False,
        show_error=True,
        quiet=False
    )
