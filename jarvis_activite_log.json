[{"timestamp": "2025-06-24T17:01:43.267869", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 0.0%, RAM: 88.3%", "details": {"cpu_percent": 0.0, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:02:13.662952", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 51.9%, RAM: 88.3%", "details": {"cpu_percent": 51.9, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:02:43.929573", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.2%, RAM: 88.5%", "details": {"cpu_percent": 37.2, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:03:14.059443", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.3%, RAM: 88.5%", "details": {"cpu_percent": 30.3, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:03:44.341523", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.9%, RAM: 88.6%", "details": {"cpu_percent": 30.9, "memory_percent": 88.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:04:14.989342", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 53.9%, RAM: 88.4%", "details": {"cpu_percent": 53.9, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:04:45.633136", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 36.9%, RAM: 88.5%", "details": {"cpu_percent": 36.9, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:15.717718", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 22.6%, RAM: 88.1%", "details": {"cpu_percent": 22.6, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:45.875706", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.1%, RAM: 88.4%", "details": {"cpu_percent": 30.1, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:57.159654", "type": "test_manuel", "description": "Test d'enregistrement d'activité manuelle", "details": {"test": true, "timestamp": "2025-06-24T17:05:57.156339"}}, {"timestamp": "2025-06-24T17:06:16.009552", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 38.1%, RAM: 88.4%", "details": {"cpu_percent": 38.1, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:06:25.202472", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 0.0%, RAM: 88.4%", "details": {"cpu_percent": 0.0, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:06:38.202641", "type": "reflexion_autonome", "description": "Réflexion sur l'optimisation des algorithmes", "details": {"simulation": true, "index": 0}}, {"timestamp": "2025-06-24T17:06:38.309836", "type": "generation_creative", "description": "Génération d'idées créatives pour interface", "details": {"simulation": true, "index": 1}}, {"timestamp": "2025-06-24T17:06:38.441036", "type": "analyse_memoire", "description": "Analyse et consolidation mémoire thermique", "details": {"simulation": true, "index": 2}}, {"timestamp": "2025-06-24T17:06:38.547381", "type": "optimisation_systeme", "description": "Optimisation performance CPU et RAM", "details": {"simulation": true, "index": 3}}, {"timestamp": "2025-06-24T17:06:38.656921", "type": "veille_technologique", "description": "Surveillance nouvelles technologies IA", "details": {"simulation": true, "index": 4}}, {"timestamp": "2025-06-24T17:06:38.761320", "type": "brainstorming", "description": "Session de brainstorming créatif autonome", "details": {"simulation": true, "index": 5}}, {"timestamp": "2025-06-24T17:06:38.898979", "type": "apprentissage", "description": "Apprentissage de nouveaux patterns utilisateur", "details": {"simulation": true, "index": 6}}, {"timestamp": "2025-06-24T17:06:39.012913", "type": "maintenance", "description": "Maintenance automatique des systèmes", "details": {"simulation": true, "index": 7}}, {"timestamp": "2025-06-24T17:06:46.171046", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 55.7%, RAM: 88.4%", "details": {"cpu_percent": 55.7, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:07:16.764767", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 50.4%, RAM: 88.4%", "details": {"cpu_percent": 50.4, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:07:46.996261", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 62.7%, RAM: 88.0%", "details": {"cpu_percent": 62.7, "memory_percent": 88.0, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:08:17.218193", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.2%, RAM: 87.2%", "details": {"cpu_percent": 40.2, "memory_percent": 87.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:08:47.802587", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 35.3%, RAM: 88.4%", "details": {"cpu_percent": 35.3, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:09:18.372446", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 28.4%, RAM: 86.6%", "details": {"cpu_percent": 28.4, "memory_percent": 86.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:09:48.780820", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 20.2%, RAM: 87.9%", "details": {"cpu_percent": 20.2, "memory_percent": 87.9, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:10:19.124261", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 13.7%, RAM: 87.7%", "details": {"cpu_percent": 13.7, "memory_percent": 87.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:10:49.306222", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.5%, RAM: 87.7%", "details": {"cpu_percent": 37.5, "memory_percent": 87.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:11:19.421704", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 28.8%, RAM: 84.8%", "details": {"cpu_percent": 28.8, "memory_percent": 84.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:11:49.539850", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 49.5%, RAM: 86.2%", "details": {"cpu_percent": 49.5, "memory_percent": 86.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:12:19.930046", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 42.0%, RAM: 88.4%", "details": {"cpu_percent": 42.0, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:12:50.275487", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 49.5%, RAM: 84.4%", "details": {"cpu_percent": 49.5, "memory_percent": 84.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:13:20.867907", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 49.6%, RAM: 84.5%", "details": {"cpu_percent": 49.6, "memory_percent": 84.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:13:51.081514", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 47.8%, RAM: 88.4%", "details": {"cpu_percent": 47.8, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:14:21.281843", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 47.9%, RAM: 87.9%", "details": {"cpu_percent": 47.9, "memory_percent": 87.9, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:14:51.552695", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 41.4%, RAM: 88.2%", "details": {"cpu_percent": 41.4, "memory_percent": 88.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:15:21.722900", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 39.3%, RAM: 86.7%", "details": {"cpu_percent": 39.3, "memory_percent": 86.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:15:53.512830", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 46.6%, RAM: 87.0%", "details": {"cpu_percent": 46.6, "memory_percent": 87.0, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:16:23.985674", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 46.3%, RAM: 88.1%", "details": {"cpu_percent": 46.3, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:16:54.137007", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 60.0%, RAM: 86.6%", "details": {"cpu_percent": 60.0, "memory_percent": 86.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:17:24.268740", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 58.5%, RAM: 88.2%", "details": {"cpu_percent": 58.5, "memory_percent": 88.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:17:54.915972", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 39.8%, RAM: 84.3%", "details": {"cpu_percent": 39.8, "memory_percent": 84.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:18:25.515701", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 33.0%, RAM: 88.1%", "details": {"cpu_percent": 33.0, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:18:55.973741", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 38.8%, RAM: 88.3%", "details": {"cpu_percent": 38.8, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:19:26.336916", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 59.3%, RAM: 88.2%", "details": {"cpu_percent": 59.3, "memory_percent": 88.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:19:56.946903", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 65.4%, RAM: 88.3%", "details": {"cpu_percent": 65.4, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:20:28.010342", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 50.7%, RAM: 89.6%", "details": {"cpu_percent": 50.7, "memory_percent": 89.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:20:58.123709", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 35.0%, RAM: 86.3%", "details": {"cpu_percent": 35.0, "memory_percent": 86.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:21:28.180068", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 35.7%, RAM: 75.0%", "details": {"cpu_percent": 35.7, "memory_percent": 75.0, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:21:58.291431", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 16.6%, RAM: 76.7%", "details": {"cpu_percent": 16.6, "memory_percent": 76.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:22:28.413384", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 21.6%, RAM: 85.8%", "details": {"cpu_percent": 21.6, "memory_percent": 85.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:22:58.671205", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 16.3%, RAM: 87.5%", "details": {"cpu_percent": 16.3, "memory_percent": 87.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:23:28.819951", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 21.7%, RAM: 88.5%", "details": {"cpu_percent": 21.7, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:23:58.992044", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 15.4%, RAM: 88.3%", "details": {"cpu_percent": 15.4, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:24:29.408798", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 22.6%, RAM: 84.3%", "details": {"cpu_percent": 22.6, "memory_percent": 84.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:24:59.647150", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.3%, RAM: 79.9%", "details": {"cpu_percent": 40.3, "memory_percent": 79.9, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:25:30.383931", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 31.2%, RAM: 82.7%", "details": {"cpu_percent": 31.2, "memory_percent": 82.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:26:00.901933", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 20.4%, RAM: 86.4%", "details": {"cpu_percent": 20.4, "memory_percent": 86.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:26:31.208814", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 23.0%, RAM: 88.6%", "details": {"cpu_percent": 23.0, "memory_percent": 88.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:27:01.348674", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 49.7%, RAM: 88.6%", "details": {"cpu_percent": 49.7, "memory_percent": 88.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:27:31.511718", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 59.6%, RAM: 85.8%", "details": {"cpu_percent": 59.6, "memory_percent": 85.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:28:01.890007", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 57.5%, RAM: 86.2%", "details": {"cpu_percent": 57.5, "memory_percent": 86.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:28:32.244538", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.3%, RAM: 88.3%", "details": {"cpu_percent": 40.3, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:29:02.589764", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 35.1%, RAM: 88.1%", "details": {"cpu_percent": 35.1, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:29:32.934995", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 16.2%, RAM: 87.5%", "details": {"cpu_percent": 16.2, "memory_percent": 87.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:30:03.311833", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 14.5%, RAM: 86.8%", "details": {"cpu_percent": 14.5, "memory_percent": 86.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:30:33.778036", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 17.6%, RAM: 84.5%", "details": {"cpu_percent": 17.6, "memory_percent": 84.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:31:04.631686", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.0%, RAM: 87.7%", "details": {"cpu_percent": 37.0, "memory_percent": 87.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:31:34.716565", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 41.1%, RAM: 83.4%", "details": {"cpu_percent": 41.1, "memory_percent": 83.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:32:04.800956", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 36.7%, RAM: 88.4%", "details": {"cpu_percent": 36.7, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:32:34.925081", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 22.9%, RAM: 83.7%", "details": {"cpu_percent": 22.9, "memory_percent": 83.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:33:05.349174", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 27.3%, RAM: 86.4%", "details": {"cpu_percent": 27.3, "memory_percent": 86.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:33:35.450009", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 26.9%, RAM: 85.1%", "details": {"cpu_percent": 26.9, "memory_percent": 85.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:34:05.535821", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.3%, RAM: 83.8%", "details": {"cpu_percent": 40.3, "memory_percent": 83.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:34:35.660531", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 26.9%, RAM: 85.0%", "details": {"cpu_percent": 26.9, "memory_percent": 85.0, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:35:05.767273", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 22.7%, RAM: 82.4%", "details": {"cpu_percent": 22.7, "memory_percent": 82.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:35:35.861022", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 25.2%, RAM: 81.7%", "details": {"cpu_percent": 25.2, "memory_percent": 81.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:36:05.957419", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 27.2%, RAM: 88.1%", "details": {"cpu_percent": 27.2, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:36:36.070656", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 42.4%, RAM: 87.4%", "details": {"cpu_percent": 42.4, "memory_percent": 87.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:37:06.157687", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.5%, RAM: 86.3%", "details": {"cpu_percent": 40.5, "memory_percent": 86.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:37:36.384182", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.5%, RAM: 88.1%", "details": {"cpu_percent": 37.5, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:38:07.147602", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 14.5%, RAM: 88.4%", "details": {"cpu_percent": 14.5, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:38:37.570423", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 13.7%, RAM: 79.3%", "details": {"cpu_percent": 13.7, "memory_percent": 79.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:39:07.972710", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 11.8%, RAM: 83.4%", "details": {"cpu_percent": 11.8, "memory_percent": 83.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:39:38.381603", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 27.9%, RAM: 86.2%", "details": {"cpu_percent": 27.9, "memory_percent": 86.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:40:08.932193", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 26.3%, RAM: 80.5%", "details": {"cpu_percent": 26.3, "memory_percent": 80.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}, {"timestamp": "2025-06-24T17:40:39.134144", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 16.3%, RAM: 88.3%", "details": {"cpu_percent": 16.3, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 6}}]