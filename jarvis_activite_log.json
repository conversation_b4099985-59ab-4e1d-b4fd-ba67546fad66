[{"timestamp": "2025-06-24T17:01:43.267869", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 0.0%, RAM: 88.3%", "details": {"cpu_percent": 0.0, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:02:13.662952", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 51.9%, RAM: 88.3%", "details": {"cpu_percent": 51.9, "memory_percent": 88.3, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:02:43.929573", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.2%, RAM: 88.5%", "details": {"cpu_percent": 37.2, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:03:14.059443", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.3%, RAM: 88.5%", "details": {"cpu_percent": 30.3, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:03:44.341523", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.9%, RAM: 88.6%", "details": {"cpu_percent": 30.9, "memory_percent": 88.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:04:14.989342", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 53.9%, RAM: 88.4%", "details": {"cpu_percent": 53.9, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:04:45.633136", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 36.9%, RAM: 88.5%", "details": {"cpu_percent": 36.9, "memory_percent": 88.5, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:15.717718", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 22.6%, RAM: 88.1%", "details": {"cpu_percent": 22.6, "memory_percent": 88.1, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:45.875706", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 30.1%, RAM: 88.4%", "details": {"cpu_percent": 30.1, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:05:57.159654", "type": "test_manuel", "description": "Test d'enregistrement d'activité manuelle", "details": {"test": true, "timestamp": "2025-06-24T17:05:57.156339"}}, {"timestamp": "2025-06-24T17:06:16.009552", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 38.1%, RAM: 88.4%", "details": {"cpu_percent": 38.1, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:06:25.202472", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 0.0%, RAM: 88.4%", "details": {"cpu_percent": 0.0, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:06:38.202641", "type": "reflexion_autonome", "description": "Réflexion sur l'optimisation des algorithmes", "details": {"simulation": true, "index": 0}}, {"timestamp": "2025-06-24T17:06:38.309836", "type": "generation_creative", "description": "Génération d'idées créatives pour interface", "details": {"simulation": true, "index": 1}}, {"timestamp": "2025-06-24T17:06:38.441036", "type": "analyse_memoire", "description": "Analyse et consolidation mémoire thermique", "details": {"simulation": true, "index": 2}}, {"timestamp": "2025-06-24T17:06:38.547381", "type": "optimisation_systeme", "description": "Optimisation performance CPU et RAM", "details": {"simulation": true, "index": 3}}, {"timestamp": "2025-06-24T17:06:38.656921", "type": "veille_technologique", "description": "Surveillance nouvelles technologies IA", "details": {"simulation": true, "index": 4}}, {"timestamp": "2025-06-24T17:06:38.761320", "type": "brainstorming", "description": "Session de brainstorming créatif autonome", "details": {"simulation": true, "index": 5}}, {"timestamp": "2025-06-24T17:06:38.898979", "type": "apprentissage", "description": "Apprentissage de nouveaux patterns utilisateur", "details": {"simulation": true, "index": 6}}, {"timestamp": "2025-06-24T17:06:39.012913", "type": "maintenance", "description": "Maintenance automatique des systèmes", "details": {"simulation": true, "index": 7}}, {"timestamp": "2025-06-24T17:06:46.171046", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 55.7%, RAM: 88.4%", "details": {"cpu_percent": 55.7, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:07:16.764767", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 50.4%, RAM: 88.4%", "details": {"cpu_percent": 50.4, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:07:46.996261", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 62.7%, RAM: 88.0%", "details": {"cpu_percent": 62.7, "memory_percent": 88.0, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 4}}, {"timestamp": "2025-06-24T17:08:17.218193", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 40.2%, RAM: 87.2%", "details": {"cpu_percent": 40.2, "memory_percent": 87.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:08:47.802587", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 35.3%, RAM: 88.4%", "details": {"cpu_percent": 35.3, "memory_percent": 88.4, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:09:18.372446", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 28.4%, RAM: 86.6%", "details": {"cpu_percent": 28.4, "memory_percent": 86.6, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:09:48.780820", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 20.2%, RAM: 87.9%", "details": {"cpu_percent": 20.2, "memory_percent": 87.9, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:10:19.124261", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 13.7%, RAM: 87.7%", "details": {"cpu_percent": 13.7, "memory_percent": 87.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:10:49.306222", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 37.5%, RAM: 87.7%", "details": {"cpu_percent": 37.5, "memory_percent": 87.7, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:11:19.421704", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 28.8%, RAM: 84.8%", "details": {"cpu_percent": 28.8, "memory_percent": 84.8, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}, {"timestamp": "2025-06-24T17:11:49.539850", "type": "surveillance_periodique", "description": "Surveillance automatique - CPU: 49.5%, RAM: 86.2%", "details": {"cpu_percent": 49.5, "memory_percent": 86.2, "activite_memoire": {"neurones_totaux": 507, "neurones_recents": 2, "derniere_activite": {"neuron_id": "aa9db263-edff-4448-87a6-0ec84abab51d", "activation_timestamp": "2025-06-24T21:01:29.042118+00:00", "local_timestamp": "2025-06-24T23:01:29.042118+02:00", "calendar_data": {"date": "2025-06-24", "time": "23:01:29", "day_of_week": "Tuesday", "day_of_year": 175, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "GOAP System Startup", "agent_response": "Moteur de planification GOAP initialisé avec succès dans JARVIS", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "b78a327a-6dd9-4f27-84ea-978808168ba7"}, "neuron_metadata": {"sujet": "Goap System Startup", "keywords": ["goap", "system", "startup", "moteur", "planification"], "complexity": 3.49, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 0.75, "memory_priority": 5.0, "retention_score": 1.71}, "temporal_links": {"previous_day": "2025-06-23", "next_day": "2025-06-25", "same_time_yesterday": "2025-06-23T23:01:29.042118+02:00", "weekly_pattern": "Tuesday-23"}}}, "pensees_detectees": 0, "processus_jarvis": 5}}]