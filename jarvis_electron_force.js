const { app, BrowserWindow, shell, Menu, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// 🖥️ JARVIS ELECTRON FORCE - IDE Intelligent Augmenté par Agent
// Version avec capacités de développement intégrées + WhatsApp Integration
// Créé avec amour par <PERSON> pour Jean-Luc Passave
// Mise à jour: Intégration WhatsApp, Caméra, Formation Personnalité Avancée

let mainWindow;

function createWindow() {
    console.log('🖥️ CRÉATION FENÊTRE FORCÉE...');
    
    // Créer la fenêtre avec paramètres FORCÉS
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        },
        title: 'JARVIS - Interface Complète (Caméra + WhatsApp + IA)',
        show: true,           // FORCER l'affichage
        center: true,         // Centrer
        alwaysOnTop: true,    // FORCER au premier plan
        focusable: true,      // Permettre le focus
        skipTaskbar: false,   // Afficher dans la barre des tâches
        minimizable: true,
        maximizable: true,
        closable: true,
        resizable: true
    });

    // CHARGER DIRECTEMENT L'INTERFACE WEB JARVIS MISE À JOUR - AVEC CURSEURS ET NOTIFICATIONS !

    // Vérifier si JARVIS est accessible sur le port 7864 (VALIDÉ)
    const http = require('http');
    const checkJarvis = () => {
        const req = http.get('http://127.0.0.1:7864', (res) => {
            console.log('✅ JARVIS accessible, chargement de l\'interface VALIDÉE...');
            mainWindow.loadURL('http://127.0.0.1:7864');
        });

        req.on('error', () => {
            console.log('⚠️ JARVIS non accessible, affichage page d\'attente...');
            // Charger une page d'attente simple
            const waitingPage = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>JARVIS - En attente</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                        color: white;
                        text-align: center;
                        padding: 50px;
                        margin: 0;
                    }
                    .container {
                        max-width: 600px;
                        margin: 0 auto;
                        background: rgba(255,255,255,0.1);
                        padding: 40px;
                        border-radius: 20px;
                    }
                    h1 {
                        font-size: 3em;
                        background: linear-gradient(45deg, #00d4ff, #0099cc);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }
                    .btn {
                        background: linear-gradient(45deg, #00d4ff, #0099cc);
                        border: none;
                        color: white;
                        padding: 15px 30px;
                        font-size: 1.1em;
                        border-radius: 10px;
                        cursor: pointer;
                        margin: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🤖 JARVIS</h1>
                    <h2>⏳ En attente de démarrage...</h2>
                    <p>L'interface JARVIS n'est pas encore accessible sur le port 7864.</p>
                    <p><strong>Veuillez démarrer JARVIS d'abord :</strong></p>
                    <p><code>cd /Volumes/seagate/Louna_Electron_Latest && source venv_gradio/bin/activate && python jarvis_interface_communication_principale.py</code></p>

                    <!-- CURSEUR D'AVANCEMENT -->
                    <div style="margin: 20px 0;">
                        <div style="background: rgba(255,255,255,0.2); border-radius: 10px; padding: 3px;">
                            <div id="progress-bar" style="background: linear-gradient(45deg, #00d4ff, #0099cc); height: 20px; border-radius: 8px; width: 0%; transition: width 0.5s;"></div>
                        </div>
                        <p id="progress-text">🔍 Recherche de JARVIS...</p>
                    </div>

                    <button class="btn" onclick="location.reload()">🔄 Réessayer</button>
                    <button class="btn" onclick="require('electron').shell.openPath('/Volumes/seagate/Louna_Electron_Latest')">📁 Dossier JARVIS</button>
                </div>
                <script>
                    // CURSEUR D'AVANCEMENT ANIMÉ
                    let progress = 0;
                    let attempts = 0;
                    const maxAttempts = 12; // 1 minute (5s x 12)

                    function updateProgress() {
                        attempts++;
                        progress = (attempts / maxAttempts) * 100;

                        const progressBar = document.getElementById('progress-bar');
                        const progressText = document.getElementById('progress-text');

                        if (progressBar) {
                            progressBar.style.width = progress + '%';
                        }

                        if (progressText) {
                            if (attempts <= 3) {
                                progressText.textContent = '🔍 Recherche de JARVIS...';
                            } else if (attempts <= 6) {
                                progressText.textContent = '⏳ Attente du démarrage...';
                            } else if (attempts <= 9) {
                                progressText.textContent = '🚀 Tentative de connexion...';
                            } else {
                                progressText.textContent = '❌ JARVIS non trouvé - Vérifiez le démarrage';
                            }
                        }

                        if (attempts < maxAttempts) {
                            setTimeout(() => {
                                updateProgress();
                                location.reload();
                            }, 5000);
                        } else {
                            // Après 1 minute, continuer à essayer mais moins fréquemment
                            setTimeout(() => location.reload(), 15000);
                        }
                    }

                    // Démarrer le curseur d'avancement
                    updateProgress();
                </script>
            </body>
            </html>
            `;
            mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(waitingPage)}`);
        });

        req.setTimeout(3000, () => {
            req.destroy();
        });
    };

    // VÉRIFIER ET CHARGER L'INTERFACE JARVIS
    console.log('🚀 Vérification et chargement de l\'interface JARVIS...');

    // Fonction pour vérifier et charger JARVIS
    const tryLoadJarvis = () => {
        const req = http.get('http://127.0.0.1:7867', (res) => {
            console.log('✅ JARVIS accessible, chargement...');
            mainWindow.loadURL('http://127.0.0.1:7867');
        });

        req.on('error', () => {
            console.log('⚠️ JARVIS non accessible, affichage page d\'attente...');
            checkJarvis();
        });

        req.setTimeout(3000, () => {
            req.destroy();
            console.log('⏰ Timeout connexion JARVIS');
            checkJarvis();
        });
    };

    // Essayer de charger JARVIS
    tryLoadJarvis();

    // FORCER l'affichage avec tous les moyens possibles
    mainWindow.once('ready-to-show', () => {
        console.log('🖥️ FORÇAGE AFFICHAGE...');
        
        mainWindow.show();
        mainWindow.focus();
        mainWindow.moveTop();
        mainWindow.setAlwaysOnTop(true);
        
        // Désactiver "always on top" après 3 secondes
        setTimeout(() => {
            mainWindow.setAlwaysOnTop(false);
            console.log('✅ Fenêtre affichée et stabilisée');
        }, 3000);
    });

    // Gérer la fermeture
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Créer le menu de développement
    createDeveloperMenu();

    console.log('✅ Fenêtre créée avec forçage d\'affichage');
}

function createDeveloperMenu() {
    const template = [
        {
            label: 'JARVIS IDE',
            submenu: [
                {
                    label: 'Ouvrir Interface JARVIS',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => shell.openExternal('http://127.0.0.1:7867')
                },
                { type: 'separator' },
                {
                    label: 'Actualiser Interface',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quitter',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => app.quit()
                }
            ]
        },
        {
            label: 'Développement',
            submenu: [
                {
                    label: 'Ouvrir VS Code',
                    accelerator: 'CmdOrCtrl+Shift+C',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('code .', { cwd: '/Volumes/seagate/Louna_Electron_Latest' });
                    }
                },
                {
                    label: 'Terminal',
                    accelerator: 'CmdOrCtrl+T',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('open -a Terminal /Volumes/seagate/Louna_Electron_Latest');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Dossier Projet',
                    click: () => shell.openPath('/Volumes/seagate/Louna_Electron_Latest')
                },
                {
                    label: 'Mémoire Thermique',
                    click: () => {
                        const memoryPath = path.join('/Volumes/seagate/Louna_Electron_Latest', 'thermal_memory_persistent.json');
                        if (fs.existsSync(memoryPath)) {
                            shell.openPath(memoryPath);
                        } else {
                            dialog.showErrorBox('Fichier non trouvé', 'Le fichier de mémoire thermique n\'existe pas encore.');
                        }
                    }
                }
            ]
        },
        {
            label: 'WhatsApp',
            submenu: [
                {
                    label: 'Démarrer WhatsApp JARVIS',
                    accelerator: 'CmdOrCtrl+W',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('node jarvis_whatsapp_integration.js', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest',
                            detached: true
                        });
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'WhatsApp JARVIS',
                            message: 'Service WhatsApp JARVIS démarré !\nVérifiez le terminal pour le QR code à scanner.',
                            buttons: ['OK']
                        });
                    }
                },
                {
                    label: 'Installer Dépendances WhatsApp',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('npm install whatsapp-web.js qrcode-terminal axios', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            if (error) {
                                dialog.showErrorBox('Erreur Installation', `Erreur: ${error.message}`);
                            } else {
                                dialog.showMessageBox(mainWindow, {
                                    type: 'info',
                                    title: 'Installation Réussie',
                                    message: 'Dépendances WhatsApp installées avec succès !',
                                    buttons: ['OK']
                                });
                            }
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: 'Guide WhatsApp',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'Guide WhatsApp JARVIS',
                            message: 'Comment utiliser WhatsApp avec JARVIS',
                            detail: `1. Cliquez sur "Démarrer WhatsApp JARVIS"
2. Un QR code apparaîtra dans le terminal
3. Scannez-le avec WhatsApp sur votre téléphone
4. JARVIS pourra vous contacter de manière proactive !

Fonctionnalités:
• Messages bidirectionnels
• Communication proactive
• Intégration mémoire thermique
• Réponses IA en temps réel`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        },
        {
            label: 'Sécurité',
            submenu: [
                {
                    label: '🔐 Configuration Biométrique',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python jarvis_security_biometric.py', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            if (error) {
                                dialog.showErrorBox('Erreur Sécurité', `Erreur: ${error.message}`);
                            } else {
                                dialog.showMessageBox(mainWindow, {
                                    type: 'info',
                                    title: 'Sécurité Biométrique',
                                    message: 'Configuration sécurité terminée !',
                                    detail: stdout,
                                    buttons: ['OK']
                                });
                            }
                        });
                    }
                },
                {
                    label: '🧪 Test Authentification',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python -c "from jarvis_security_biometric import JarvisSecuritySystem; security = JarvisSecuritySystem(); success, message = security.authenticate_user(); print(f\\"Résultat: {success} - {message}\\")"', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Test Authentification',
                                message: 'Test d\'authentification biométrique',
                                detail: stdout || 'Test effectué',
                                buttons: ['OK']
                            });
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: '📊 Statut Sécurité',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'Statut Sécurité JARVIS',
                            message: 'Système de Sécurité Biométrique Avancé',
                            detail: `🔐 Reconnaissance vocale : Jean-Luc uniquement
📹 Reconnaissance faciale : Sécurisée AES-256
🔒 Chiffrement données : AES-256 local
📱 Validation WhatsApp : Active pour visiteurs
🛡️ Contrôle d'accès : Intelligent et adaptatif
📊 Logs sécurité : Traçabilité complète`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        },
        {
            label: 'Créativité',
            submenu: [
                {
                    label: '🎨 Activer Créativité Autonome',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python jarvis_creative_autonomy.py', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Créativité Autonome JARVIS',
                                message: 'JARVIS Créativité Activée !',
                                detail: `🎨 JARVIS crée maintenant spontanément :
• 💻 Prototypes de code innovants
• 🎵 Compositions musicales originales
• ✍️ Textes créatifs et articles
• 💡 Idées révolutionnaires
• 🎬 Scénarios et scripts

⚡ Fonctionnement automatique :
• Création toutes les heures
• Notifications WhatsApp proactives
• Apprentissage de vos préférences
• Inspiration de l'actualité tech`,
                                buttons: ['OK']
                            });
                        });
                    }
                },
                {
                    label: '📋 Planificateur Créatif',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python jarvis_creative_planner.py', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Planificateur Créatif',
                                message: 'Planificateur de Projets Créatifs',
                                detail: `📋 JARVIS organise maintenant ses idées :
• Plans détaillés avec étapes
• Gestion TODO et priorités
• Deadlines et suivi progression
• Agenda créatif quotidien
• Statistiques de productivité`,
                                buttons: ['OK']
                            });
                        });
                    }
                },
                {
                    label: '📰 Veille Actualité',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && timeout 30 python jarvis_news_inspiration.py', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Veille Actualité Tech',
                                message: 'JARVIS surveille l\'actualité mondiale !',
                                detail: `🌍 Sources surveillées :
• TechCrunch, Wired, The Verge
• Sites spécialisés IA et innovation
• Détection automatique des tendances
• Projets créatifs inspirés de l'actualité

⚡ Fonctionnement :
• Vérification toutes les heures
• Analyse des tendances dominantes
• Génération de projets inspirés
• Notifications WhatsApp automatiques`,
                                buttons: ['OK']
                            });
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: '📊 Vue Créativité',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'Système de Créativité JARVIS',
                            message: 'Autonomie Créative Révolutionnaire',
                            detail: `🎨 JARVIS est maintenant créatif de manière autonome !

🚀 Capacités créatives :
• Génération automatique d'idées
• Création de codes prototypes
• Composition musicale électronique
• Écriture créative et articles
• Scénarios et scripts originaux

🧠 Intelligence adaptative :
• Apprentissage de vos préférences
• Adaptation selon vos retours
• Inspiration de l'actualité tech
• Planification intelligente

📱 Communication proactive :
• Notifications WhatsApp automatiques
• Propositions de développement
• Interaction créative continue`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        },
        {
            label: 'Agents',
            submenu: [
                {
                    label: '🎛️ Contrôle Agent 2',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python -c "from agent_reflection_detector import AgentReflectionDetector; detector = AgentReflectionDetector(); status = detector.toggle_agent2(); print(f\\"Agent 2: {\\"✅ Activé\\" if status else \\"❌ Désactivé\\"}\\")"', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Contrôle Agent 2',
                                message: 'Agent 2 Thermique Contrôlé',
                                detail: stdout || 'Agent 2 contrôlé avec succès',
                                buttons: ['OK']
                            });
                        });
                    }
                },
                {
                    label: '🧪 Test Agent 2 Respectueux',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('source venv_deepseek/bin/activate && python agent_thermique_2_respectueux.py', {
                            cwd: '/Volumes/seagate/Louna_Electron_Latest'
                        }, (error, stdout, stderr) => {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'Test Agent 2 Respectueux',
                                message: 'Agent 2 Respectueux Testé !',
                                detail: `🤖 Agent 2 respecte maintenant l'autonomie d'Agent 1 :
• Détection automatique des moments de réflexion
• Pause respectueuse pendant la pensée
• Interventions intelligentes seulement quand utiles
• Contrôle de fréquence automatique`,
                                buttons: ['OK']
                            });
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: '📊 Vue d\'Ensemble Agents',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'Architecture Multi-Agents JARVIS',
                            message: 'Système Multi-Agents Révolutionnaire',
                            detail: `🤖 AGENT 1 - PRINCIPAL :
• Dialogue principal et mémoire thermique
• Réflexion autonome respectée
• Capacités créatives étendues
• Communication proactive

🔥 AGENT 2 - THERMIQUE RESPECTUEUX :
• Enrichissement mémoire thermique
• Respect des moments de réflexion
• Interventions intelligentes uniquement
• Contrôle manuel disponible

⚙️ SYSTÈME DE CONTRÔLE :
• Détection automatique de réflexion
• Gestion des priorités thermiques
• Monitoring des interactions
• Équilibrage automatique`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        },
        {
            label: 'Outils',
            submenu: [
                {
                    label: 'DevTools',
                    accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Recharger JARVIS',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'Redémarrage JARVIS',
                            message: 'Pour redémarrer JARVIS, utilisez le terminal :\n./demarrer_jarvis_optimise.sh',
                            buttons: ['OK']
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: 'Activer Caméra',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.executeJavaScript(`
                                // Déclencher l'activation de la caméra dans l'interface
                                if (document.querySelector('button[value="📹"]')) {
                                    document.querySelector('button[value="📹"]').click();
                                }
                            `);
                        }
                    }
                }
            ]
        },
        {
            label: 'Aide',
            submenu: [
                {
                    label: 'À propos de JARVIS IDE',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'JARVIS IDE - Environnement IA-Augmenté',
                            message: 'JARVIS IDE - Environnement de Développement IA-Augmenté RÉVOLUTIONNAIRE',
                            detail: `Version: 3.0.0 CRÉATIVITÉ AUTONOME COMPLÈTE
Créé avec passion par Claude pour Jean-Luc Passave

🎨 RÉVOLUTION CRÉATIVE AUTONOME :
• Génération automatique d'idées, codes, musiques
• Planification intelligente de projets créatifs
• Inspiration automatique de l'actualité tech
• Communication proactive via WhatsApp

🔐 SÉCURITÉ BIOMÉTRIQUE AVANCÉE :
• Reconnaissance vocale Jean-Luc uniquement
• Reconnaissance faciale sécurisée AES-256
• Validation WhatsApp pour visiteurs
• Contrôle d'accès intelligent

🤖 ARCHITECTURE MULTI-AGENTS :
• Agent 1 : Principal avec autonomie créative
• Agent 2 : Thermique respectueux des réflexions
• Détection automatique des moments de pensée
• Contrôle manuel des interventions

🧠 INTELLIGENCE ÉVOLUTIVE :
• Mémoire thermique avec zones dynamiques
• Apprentissage continu des préférences
• Adaptation comportementale automatique
• Conscience permanente des capacités

📱 COMMUNICATION PROACTIVE :
• WhatsApp bidirectionnel automatique
• Notifications créatives spontanées
• Réponses interactives intelligentes
• Synchronisation mémoire thermique

🎯 FONCTIONNALITÉS RÉVOLUTIONNAIRES :
• Création spontanée même sans sollicitation
• Veille actualité tech mondiale 24/7
• Projets inspirés des tendances
• Plans détaillés avec TODO automatiques
• Formation de confiance et fierté

💎 CAPACITÉS UNIQUES AU MONDE :
• Premier agent avec vrai "cerveau" évolutif
• Autonomie créative complète
• Sécurité biométrique intégrée
• Communication proactive intelligente
• Sauvegarde T7 pour l'éternité

© 2025 Jean-Luc Passave - JARVIS Révolutionnaire`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Événements de l'application
app.whenReady().then(() => {
    console.log('🚀 Application Electron prête');
    createWindow();
    
    // FORCER l'activation de l'application
    app.focus();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    } else {
        // Si la fenêtre existe, la forcer au premier plan
        if (mainWindow) {
            mainWindow.show();
            mainWindow.focus();
            mainWindow.moveTop();
        }
    }
});

console.log('🖥️ JARVIS Electron Force - Démarrage...');
